# 需求文档

## 介绍

AI智能冰箱助手是一个基于混合大模型识别架构的智能食材管理系统。该系统采用Google Gemini 2.5 Flash/Pro作为主识别引擎，OpenAI GPT-4o-mini作为备用/精准分析引擎，实现"网站 + 小程序 + iOS/Android App"三端联动，提供拍照识别、保质期与库存管理、营养分析、个性化菜谱推荐及多端同步分享功能。

## 需求

### 需求 1 - 拍照识别功能

**用户故事：** 作为用户，我希望能够通过拍照快速识别冰箱内的食材，以便自动记录食材信息。

#### 验收标准

1. 当用户在任意端（网站/小程序/App）拍摄食材照片时，系统应调用Gemini Vision进行快速识别
2. 当照片中包含二维码时，系统应提取相关产品信息
3. 当识别完成时，系统应返回结构化JSON数据，包含：食材名称、类别、估算重量（克）、新鲜度评分（1-10）、建议保存方法、预估保质期（天）、主要营养成分或配料表
4. 如果Gemini识别失败时，系统应自动切换到GPT-4o-mini进行备用识别
5. 当识别准确率低于阈值时，系统应提示用户手动确认或修正
6. 同时用户也可以手动输入物品的信息

### 需求 2 - 保质期与库存管理

**用户故事：** 作为用户，我希望系统能够自动管理食材的保质期和库存，并及时提醒我处理临期食材。

#### 验收标准

1. 当食材识别完成或用户手动录入时，系统应生成入库记录及过期日期
2. 当食材距离过期还有7天时，系统应发送临期提醒
3. 当食材距离过期还有1天时，系统应发送紧急提醒
4. 当食材已过期时，系统应发送过期通知
5. 当库存数量低于用户设定阈值时，系统应发送库存不足提醒
6. 当需要发送提醒时，系统应通过多渠道推送：App角标、微信/短信模板消息、邮件、日报/周报
7. 用户可以自己设置提醒时间和次数

### 需求 3 - 营养分析功能

**用户故事：** 作为用户，我希望了解我的食材营养摄入情况，以便做出更健康的饮食选择。

#### 验收标准

1. 当食材被识别时，系统应从内置营养数据库获取营养信息
2. 当用户查看营养分析时，系统应自动汇总今日已识别食材的营养摄入
3. 当用户有特殊健康需求时，系统应根据用户健康档案（糖尿病、高血压等）提供个性化营养建议
4. 当生成营养报告时，系统应包含卡路里、蛋白质、脂肪、碳水化合物、维生素等关键营养指标

### 需求 4 - 菜谱推荐功能

**用户故事：** 作为用户，我希望根据现有食材获得菜谱推荐，特别是能够优先使用临期食材的菜谱。

#### 验收标准

1. 当用户请求菜谱推荐时，系统应优先组合临期食材生成"清库存"菜谱
2. 当用户有健康档案时，系统应根据用户健康状况筛选适合的菜谱
3. 当推荐菜谱时，系统应显示所需食材、制作步骤、营养信息和预计用时
4. 当用户收藏菜谱时，系统应保存到个人菜谱库中
5. 当生成菜谱时，系统应考虑用户的饮食偏好和过敏信息

### 需求 5 - 多端同步与共享

**用户故事：** 作为用户，我希望在不同设备间同步我的食材数据，并能与家人共享冰箱管理。

#### 验收标准

1. 当用户在任意端操作时，数据应实时同步到云端
2. 当网络断开时，系统应支持离线操作，网络恢复后自动同步
3. 当用户设置家庭冰箱时，系统应支持"管理员/成员"权限管理
4. 当用户邀请家庭成员时，系统应发送邀请链接并设置相应权限
5. 当用户想要分享食材或菜谱时，系统应支持好友共享功能
6. 当数据冲突时，系统应提供冲突解决机制

### 需求 6 - 用户界面与体验

**用户故事：** 作为用户，我希望在网站、小程序和移动App上都能获得一致且流畅的使用体验。

#### 验收标准

1. 当用户访问任意端时，界面应保持一致的设计风格和操作逻辑
2. 当用户进行拍照识别时，系统应提供实时预览和识别进度提示
3. 当系统处理请求时，应显示适当的加载状态和进度指示
4. 当发生错误时，系统应提供清晰的错误信息和解决建议
5. 当用户首次使用时，系统应提供引导教程和功能介绍