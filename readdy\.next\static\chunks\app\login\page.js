/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/login/page"],{

/***/ "(app-pages-browser)/../node_modules/next/dist/api/navigation.js":
/*!***************************************************!*\
  !*** ../node_modules/next/dist/api/navigation.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/../node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDs7QUFFaEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamFtZXNcXERlc2t0b3BcXGJpbmd4aWFuZ1xcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(app-pages-browser)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2phbWVzJTVDJTVDRGVza3RvcCU1QyU1Q2Jpbmd4aWFuZyU1QyU1Q3JlYWRkeSU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBdUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGphbWVzXFxcXERlc2t0b3BcXFxcYmluZ3hpYW5nXFxcXHJlYWRkeVxcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/app-dir/link.js":
/*!********************************************************!*\
  !*** ../node_modules/next/dist/client/app-dir/link.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/../node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/../node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/../node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/../node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/../node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/use-merged-ref.js":
/*!**********************************************************!*\
  !*** ../node_modules/next/dist/client/use-merged-ref.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*****************************************************************************************!*\
  !*** ../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*******************************************************************!*\
  !*** ../node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsK0xBQXNFO0FBQ3hFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGphbWVzXFxEZXNrdG9wXFxiaW5neGlhbmdcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!***********************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!*************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/../node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWxvY2FsLXVybC5qcyIsIm1hcHBpbmdzIjoiOzs7OzhDQU1nQkE7OztlQUFBQTs7O21DQU5pQzt5Q0FDckI7QUFLckIsU0FBU0EsV0FBV0MsR0FBVztJQUNwQyxnRUFBZ0U7SUFDaEUsSUFBSSxDQUFDQyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUFjRCxNQUFNLE9BQU87SUFDaEMsSUFBSTtRQUNGLDREQUE0RDtRQUM1RCxNQUFNRSxpQkFBaUJDLENBQUFBLEdBQUFBLE9BQUFBLGlCQUFBQTtRQUN2QixNQUFNQyxXQUFXLElBQUlDLElBQUlMLEtBQUtFO1FBQzlCLE9BQU9FLFNBQVNFLE1BQU0sS0FBS0osa0JBQWtCSyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZSCxTQUFTSSxRQUFRO0lBQzVFLEVBQUUsT0FBT0MsR0FBRztRQUNWLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcc3JjXFxzaGFyZWRcXGxpYlxccm91dGVyXFx1dGlsc1xcaXMtbG9jYWwtdXJsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQWJzb2x1dGVVcmwsIGdldExvY2F0aW9uT3JpZ2luIH0gZnJvbSAnLi4vLi4vdXRpbHMnXG5pbXBvcnQgeyBoYXNCYXNlUGF0aCB9IGZyb20gJy4uLy4uLy4uLy4uL2NsaWVudC9oYXMtYmFzZS1wYXRoJ1xuXG4vKipcbiAqIERldGVjdHMgd2hldGhlciBhIGdpdmVuIHVybCBpcyByb3V0YWJsZSBieSB0aGUgTmV4dC5qcyByb3V0ZXIgKGJyb3dzZXIgb25seSkuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0xvY2FsVVJMKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIHByZXZlbnQgYSBoeWRyYXRpb24gbWlzbWF0Y2ggb24gaHJlZiBmb3IgdXJsIHdpdGggYW5jaG9yIHJlZnNcbiAgaWYgKCFpc0Fic29sdXRlVXJsKHVybCkpIHJldHVybiB0cnVlXG4gIHRyeSB7XG4gICAgLy8gYWJzb2x1dGUgdXJscyBjYW4gYmUgbG9jYWwgaWYgdGhleSBhcmUgb24gdGhlIHNhbWUgb3JpZ2luXG4gICAgY29uc3QgbG9jYXRpb25PcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpXG4gICAgY29uc3QgcmVzb2x2ZWQgPSBuZXcgVVJMKHVybCwgbG9jYXRpb25PcmlnaW4pXG4gICAgcmV0dXJuIHJlc29sdmVkLm9yaWdpbiA9PT0gbG9jYXRpb25PcmlnaW4gJiYgaGFzQmFzZVBhdGgocmVzb2x2ZWQucGF0aG5hbWUpXG4gIH0gY2F0Y2ggKF8pIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbImlzTG9jYWxVUkwiLCJ1cmwiLCJpc0Fic29sdXRlVXJsIiwibG9jYXRpb25PcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInJlc29sdmVkIiwiVVJMIiwib3JpZ2luIiwiaGFzQmFzZVBhdGgiLCJwYXRobmFtZSIsIl8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js":
/*!*****************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/utils.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/utils/error-once.js":
/*!****************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvdXRpbHMvZXJyb3Itb25jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OzZDQVdTQTs7O2VBQUFBOzs7QUFYVCxJQUFJQSxZQUFZLENBQUNDLEtBQWU7QUFDaEMsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekMsTUFBTUcsU0FBUyxJQUFJQztJQUNuQk4sWUFBWSxDQUFDTztRQUNYLElBQUksQ0FBQ0YsT0FBT0csR0FBRyxDQUFDRCxNQUFNO1lBQ3BCRSxRQUFRQyxLQUFLLENBQUNIO1FBQ2hCO1FBQ0FGLE9BQU9NLEdBQUcsQ0FBQ0o7SUFDYjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcc2hhcmVkXFxsaWJcXHV0aWxzXFxlcnJvci1vbmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlcnJvck9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3QgZXJyb3JzID0gbmV3IFNldDxzdHJpbmc+KClcbiAgZXJyb3JPbmNlID0gKG1zZzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFlcnJvcnMuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IobXNnKVxuICAgIH1cbiAgICBlcnJvcnMuYWRkKG1zZylcbiAgfVxufVxuXG5leHBvcnQgeyBlcnJvck9uY2UgfVxuIl0sIm5hbWVzIjpbImVycm9yT25jZSIsIl8iLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJlcnJvcnMiLCJTZXQiLCJtc2ciLCJoYXMiLCJjb25zb2xlIiwiZXJyb3IiLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_GlobalUI__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/GlobalUI */ \"(app-pages-browser)/./components/GlobalUI.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { showError, showLoading } = (0,_components_GlobalUI__WEBPACK_IMPORTED_MODULE_6__.useGlobalUI)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password) {\n            showError(\"请填写所有必填字段\");\n            return;\n        }\n        setLoading(true);\n        showLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.login({\n                email: formData.email,\n                password: formData.password\n            });\n            if (response.success && response.data) {\n                // 使用 AuthContext 的 login 方法\n                await login(response.data.token, response.data.user);\n                router.push(\"/\");\n            } else {\n                showError(response.error || response.message || \"登录失败，请检查邮箱和密码\");\n            }\n        } catch (error) {\n            console.error(\"登录错误:\", error);\n            showError(\"登录失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n            showLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-user-line text-2xl text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"登录您的账户\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"欢迎回到智能冰箱助手\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                                    placeholder: \"请输入邮箱地址\",\n                                                    value: formData.email,\n                                                    onChange: handleChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-mail-line text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"密码\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                                                    placeholder: \"请输入密码\",\n                                                    value: formData.password,\n                                                    onChange: handleChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-lock-line text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"登录中...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-login-circle-line mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"登录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"还没有账户？\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/register\",\n                                        className: \"font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200\",\n                                        children: \"立即注册\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"DmGnyDa5zEI3ZKLtqPYAsMbrucM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _components_GlobalUI__WEBPACK_IMPORTED_MODULE_6__.useGlobalUI\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/GlobalErrorNotification.tsx":
/*!************************************************!*\
  !*** ./components/GlobalErrorNotification.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalErrorNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useApiState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useApiState */ \"(app-pages-browser)/./hooks/useApiState.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GlobalErrorNotification() {\n    _s();\n    const { error, clearError } = (0,_hooks_useApiState__WEBPACK_IMPORTED_MODULE_2__.useGlobalError)();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalErrorNotification.useEffect\": ()=>{\n            if (error) {\n                setIsVisible(true);\n                // 自动隐藏错误通知（5秒后）\n                const timer = setTimeout({\n                    \"GlobalErrorNotification.useEffect.timer\": ()=>{\n                        setIsVisible(false);\n                        setTimeout(clearError, 300); // 等待动画完成后清除错误\n                    }\n                }[\"GlobalErrorNotification.useEffect.timer\"], 5000);\n                return ({\n                    \"GlobalErrorNotification.useEffect\": ()=>clearTimeout(timer)\n                })[\"GlobalErrorNotification.useEffect\"];\n            }\n        }\n    }[\"GlobalErrorNotification.useEffect\"], [\n        error,\n        clearError\n    ]);\n    if (!error) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 transition-all duration-300 \".concat(isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg shadow-lg p-4 max-w-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-error-warning-line text-red-600 text-xl\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-red-800\",\n                                children: \"操作失败\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-700 mt-1\",\n                                children: error.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setIsVisible(false);\n                            setTimeout(clearError, 300);\n                        },\n                        className: \"flex-shrink-0 ml-2 text-red-400 hover:text-red-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-close-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalErrorNotification.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(GlobalErrorNotification, \"cl3r/kMnSxK4XIekoyXIsHSjMqc=\", false, function() {\n    return [\n        _hooks_useApiState__WEBPACK_IMPORTED_MODULE_2__.useGlobalError\n    ];\n});\n_c = GlobalErrorNotification;\nvar _c;\n$RefreshReg$(_c, \"GlobalErrorNotification\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/GlobalErrorNotification.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/GlobalLoadingIndicator.tsx":
/*!***********************************************!*\
  !*** ./components/GlobalLoadingIndicator.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalLoadingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useApiState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useApiState */ \"(app-pages-browser)/./hooks/useApiState.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction GlobalLoadingIndicator() {\n    _s();\n    const { isAnyLoading } = (0,_hooks_useApiState__WEBPACK_IMPORTED_MODULE_1__.useGlobalLoading)();\n    if (!isAnyLoading) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-1 bg-blue-200\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full bg-blue-600 animate-pulse w-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalLoadingIndicator.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalLoadingIndicator.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalLoadingIndicator.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_s(GlobalLoadingIndicator, \"j6lhvvKwaqZVOHx0xjaI73+tj4w=\", false, function() {\n    return [\n        _hooks_useApiState__WEBPACK_IMPORTED_MODULE_1__.useGlobalLoading\n    ];\n});\n_c = GlobalLoadingIndicator;\nvar _c;\n$RefreshReg$(_c, \"GlobalLoadingIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvR2xvYmFsTG9hZGluZ0luZGljYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFdUQ7QUFFeEMsU0FBU0M7O0lBQ3RCLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUdGLG9FQUFnQkE7SUFFekMsSUFBSSxDQUFDRSxjQUFjO1FBQ2pCLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztBQUl2QjtHQWR3Qkg7O1FBQ0dELGdFQUFnQkE7OztLQURuQkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamFtZXNcXERlc2t0b3BcXGJpbmd4aWFuZ1xccmVhZGR5XFxjb21wb25lbnRzXFxHbG9iYWxMb2FkaW5nSW5kaWNhdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyB1c2VHbG9iYWxMb2FkaW5nIH0gZnJvbSAnQC9ob29rcy91c2VBcGlTdGF0ZSc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHbG9iYWxMb2FkaW5nSW5kaWNhdG9yKCkge1xyXG4gIGNvbnN0IHsgaXNBbnlMb2FkaW5nIH0gPSB1c2VHbG9iYWxMb2FkaW5nKCk7XHJcblxyXG4gIGlmICghaXNBbnlMb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC0wIGxlZnQtMCByaWdodC0wIHotNTBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEgYmctYmx1ZS0yMDBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtZnVsbCBiZy1ibHVlLTYwMCBhbmltYXRlLXB1bHNlIHctZnVsbFwiPjwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsidXNlR2xvYmFsTG9hZGluZyIsIkdsb2JhbExvYWRpbmdJbmRpY2F0b3IiLCJpc0FueUxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/GlobalLoadingIndicator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/GlobalUI.tsx":
/*!*********************************!*\
  !*** ./components/GlobalUI.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _GlobalLoadingIndicator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GlobalLoadingIndicator */ \"(app-pages-browser)/./components/GlobalLoadingIndicator.tsx\");\n/* harmony import */ var _GlobalErrorNotification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobalErrorNotification */ \"(app-pages-browser)/./components/GlobalErrorNotification.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction GlobalUI() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalLoadingIndicator__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalUI.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalErrorNotification__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\components\\\\GlobalUI.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = GlobalUI;\nvar _c;\n$RefreshReg$(_c, \"GlobalUI\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvR2xvYmFsVUkudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRThEO0FBQ0U7QUFFakQsU0FBU0U7SUFDdEIscUJBQ0U7OzBCQUNFLDhEQUFDRiwrREFBc0JBOzs7OzswQkFDdkIsOERBQUNDLGdFQUF1QkE7Ozs7Ozs7QUFHOUI7S0FQd0JDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGphbWVzXFxEZXNrdG9wXFxiaW5neGlhbmdcXHJlYWRkeVxcY29tcG9uZW50c1xcR2xvYmFsVUkudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBHbG9iYWxMb2FkaW5nSW5kaWNhdG9yIGZyb20gJy4vR2xvYmFsTG9hZGluZ0luZGljYXRvcic7XHJcbmltcG9ydCBHbG9iYWxFcnJvck5vdGlmaWNhdGlvbiBmcm9tICcuL0dsb2JhbEVycm9yTm90aWZpY2F0aW9uJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdsb2JhbFVJKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8R2xvYmFsTG9hZGluZ0luZGljYXRvciAvPlxyXG4gICAgICA8R2xvYmFsRXJyb3JOb3RpZmljYXRpb24gLz5cclxuICAgIDwvPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiR2xvYmFsTG9hZGluZ0luZGljYXRvciIsIkdsb2JhbEVycm9yTm90aWZpY2F0aW9uIiwiR2xvYmFsVUkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/GlobalUI.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// 创建认证上下文\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    token: null,\n    isAuthenticated: false,\n    isLoading: true,\n    login: async ()=>{},\n    logout: ()=>{},\n    refreshUser: async ()=>{}\n});\n// 自定义Hook用于使用认证上下文\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 认证提供者组件\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 计算认证状态\n    const isAuthenticated = !!user && !!token;\n    // 登录函数\n    const login = async (newToken, newUser)=>{\n        try {\n            // 保存到localStorage\n            localStorage.setItem('authToken', newToken);\n            localStorage.setItem('user', JSON.stringify(newUser));\n            // 更新状态\n            setToken(newToken);\n            setUser(newUser);\n            console.log('用户登录成功:', newUser.username);\n        } catch (error) {\n            console.error('登录状态保存失败:', error);\n            throw error;\n        }\n    };\n    // 登出函数\n    const logout = ()=>{\n        try {\n            // 清除localStorage\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            // 清除状态\n            setToken(null);\n            setUser(null);\n            console.log('用户已登出');\n        } catch (error) {\n            console.error('登出失败:', error);\n        }\n    };\n    // 刷新用户信息\n    const refreshUser = async ()=>{\n        if (!token) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getMe();\n            if (response.success && response.data) {\n                const updatedUser = response.data;\n                setUser(updatedUser);\n                localStorage.setItem('user', JSON.stringify(updatedUser));\n                console.log('用户信息已刷新');\n            } else {\n                console.warn('刷新用户信息失败:', response.error);\n                // 如果获取用户信息失败，可能token已过期，执行登出\n                logout();\n            }\n        } catch (error) {\n            console.error('刷新用户信息错误:', error);\n        // 网络错误等情况下不执行登出，保持当前状态\n        }\n    };\n    // 初始化认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        // 从localStorage恢复认证状态\n                        const savedToken = localStorage.getItem('authToken');\n                        const savedUser = localStorage.getItem('user');\n                        if (savedToken && savedUser) {\n                            try {\n                                const parsedUser = JSON.parse(savedUser);\n                                setToken(savedToken);\n                                setUser(parsedUser);\n                                // 验证token是否仍然有效\n                                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getMe();\n                                if (response.success && response.data) {\n                                    // Token有效，更新用户信息\n                                    const currentUser = response.data;\n                                    setUser(currentUser);\n                                    localStorage.setItem('user', JSON.stringify(currentUser));\n                                    console.log('认证状态已恢复:', currentUser.username);\n                                } else {\n                                    // Token无效，清除认证状态\n                                    console.warn('Token已过期，清除认证状态');\n                                    logout();\n                                }\n                            } catch (parseError) {\n                                console.error('解析用户数据失败:', parseError);\n                                logout();\n                            }\n                        } else {\n                            console.log('未找到保存的认证信息');\n                        }\n                    } catch (error) {\n                        console.error('初始化认证状态失败:', error);\n                        // 网络错误等情况下，如果有本地数据就使用本地数据\n                        const savedToken = localStorage.getItem('authToken');\n                        const savedUser = localStorage.getItem('user');\n                        if (savedToken && savedUser) {\n                            try {\n                                const parsedUser = JSON.parse(savedUser);\n                                setToken(savedToken);\n                                setUser(parsedUser);\n                                console.log('使用本地认证数据');\n                            } catch (parseError) {\n                                console.error('解析本地用户数据失败:', parseError);\n                                logout();\n                            }\n                        }\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // 定期刷新用户信息（可选）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            // 每30分钟刷新一次用户信息\n            const interval = setInterval({\n                \"AuthProvider.useEffect.interval\": ()=>{\n                    refreshUser();\n                }\n            }[\"AuthProvider.useEffect.interval\"], 30 * 60 * 1000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearInterval(interval)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        token\n    ]);\n    const contextValue = {\n        user,\n        token,\n        isAuthenticated,\n        isLoading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bingxiang\\\\readdy\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"PiwoDeqPRCsfRwdePw8UHQ38Ar0=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useApiState.ts":
/*!******************************!*\
  !*** ./hooks/useApiState.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApiOperation: () => (/* binding */ useApiOperation),\n/* harmony export */   useApiState: () => (/* binding */ useApiState),\n/* harmony export */   useGlobalError: () => (/* binding */ useGlobalError),\n/* harmony export */   useGlobalLoading: () => (/* binding */ useGlobalLoading)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n\n\n\n// 全局错误处理Hook\nfunction useGlobalError() {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const errorTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // 自动清除错误（5秒后）\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGlobalError.useEffect\": ()=>{\n            if (error) {\n                errorTimeoutRef.current = setTimeout({\n                    \"useGlobalError.useEffect\": ()=>{\n                        clearError();\n                    }\n                }[\"useGlobalError.useEffect\"], 5000);\n            }\n            return ({\n                \"useGlobalError.useEffect\": ()=>{\n                    if (errorTimeoutRef.current) {\n                        clearTimeout(errorTimeoutRef.current);\n                    }\n                }\n            })[\"useGlobalError.useEffect\"];\n        }\n    }[\"useGlobalError.useEffect\"], [\n        error\n    ]);\n    const handleError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGlobalError.useCallback[handleError]\": (error)=>{\n            // 清除之前的超时\n            if (errorTimeoutRef.current) {\n                clearTimeout(errorTimeoutRef.current);\n                errorTimeoutRef.current = null;\n            }\n            if (error instanceof _lib_api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n                setError({\n                    message: error.message,\n                    code: error.code,\n                    status: error.status,\n                    details: error.details,\n                    timestamp: Date.now()\n                });\n                // 特殊处理认证错误\n                if (error.code === 'AUTH_EXPIRED') {\n                    // 重定向到登录页面\n                    console.warn('认证已过期，重定向到登录页面');\n                    router.push('/login');\n                }\n            } else if (error instanceof Error) {\n                setError({\n                    message: error.message,\n                    timestamp: Date.now()\n                });\n            } else {\n                setError({\n                    message: typeof error === 'string' ? error : '发生未知错误',\n                    timestamp: Date.now()\n                });\n            }\n        }\n    }[\"useGlobalError.useCallback[handleError]\"], [\n        router\n    ]);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGlobalError.useCallback[clearError]\": ()=>{\n            setError(null);\n            if (errorTimeoutRef.current) {\n                clearTimeout(errorTimeoutRef.current);\n                errorTimeoutRef.current = null;\n            }\n        }\n    }[\"useGlobalError.useCallback[clearError]\"], []);\n    return {\n        error,\n        handleError,\n        clearError\n    };\n}\n// 全局加载状态Hook\nfunction useGlobalLoading() {\n    const [loadingStates, setLoadingStates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    const [loadingCount, setLoadingCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGlobalLoading.useEffect\": ()=>{\n            const handleLoadingChange = {\n                \"useGlobalLoading.useEffect.handleLoadingChange\": (states)=>{\n                    setLoadingStates(new Map(states));\n                    setLoadingCount(states.size);\n                }\n            }[\"useGlobalLoading.useEffect.handleLoadingChange\"];\n            const unsubscribe = _lib_api__WEBPACK_IMPORTED_MODULE_1__.loadingManager.subscribe(handleLoadingChange);\n            return ({\n                \"useGlobalLoading.useEffect\": ()=>unsubscribe()\n            })[\"useGlobalLoading.useEffect\"];\n        }\n    }[\"useGlobalLoading.useEffect\"], []);\n    const isLoading = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGlobalLoading.useCallback[isLoading]\": (key)=>{\n            return _lib_api__WEBPACK_IMPORTED_MODULE_1__.loadingManager.isLoading(key);\n        }\n    }[\"useGlobalLoading.useCallback[isLoading]\"], []);\n    const isAnyLoading = loadingCount > 0;\n    return {\n        loadingStates,\n        loadingCount,\n        isLoading,\n        isAnyLoading\n    };\n}\n// 组合Hook：同时管理加载状态和错误\nfunction useApiState() {\n    const { error, handleError, clearError } = useGlobalError();\n    const { loadingStates, loadingCount, isLoading, isAnyLoading } = useGlobalLoading();\n    // 获取当前正在加载的请求列表\n    const getActiveRequests = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiState.useCallback[getActiveRequests]\": ()=>{\n            return Array.from(loadingStates.keys());\n        }\n    }[\"useApiState.useCallback[getActiveRequests]\"], [\n        loadingStates\n    ]);\n    return {\n        // 错误状态\n        error,\n        handleError,\n        clearError,\n        // 加载状态\n        loadingStates,\n        loadingCount,\n        isLoading,\n        isAnyLoading,\n        getActiveRequests\n    };\n}\n// 特定API操作的Hook，支持自动重试和取消\nfunction useApiOperation() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // 取消当前请求\n    const cancel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiOperation.useCallback[cancel]\": ()=>{\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n                abortControllerRef.current = null;\n                setLoading(false);\n            }\n        }\n    }[\"useApiOperation.useCallback[cancel]\"], []);\n    const execute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiOperation.useCallback[execute]\": async function(operation) {\n            let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n            // 取消之前的请求\n            cancel();\n            // 创建新的AbortController\n            abortControllerRef.current = new AbortController();\n            const { retries = 0, retryDelay = 1000, onSuccess, onError } = options;\n            setLoading(true);\n            setError(null);\n            let lastError = null;\n            // 重试逻辑\n            for(let attempt = 0; attempt <= retries; attempt++){\n                try {\n                    const result = await operation();\n                    if (result.success) {\n                        setData(result.data);\n                        setLoading(false);\n                        if (onSuccess) {\n                            onSuccess(result.data);\n                        }\n                        return result.data;\n                    } else {\n                        throw new Error(result.error || '操作失败');\n                    }\n                } catch (err) {\n                    lastError = err;\n                    // 如果是用户取消，不重试\n                    if (err instanceof DOMException && err.name === 'AbortError') {\n                        break;\n                    }\n                    // 判断是否应该重试\n                    const shouldRetry = attempt < retries;\n                    if (!shouldRetry) {\n                        break;\n                    }\n                    // 等待后重试\n                    console.log(\"操作失败，\".concat(attempt + 1, \"/\").concat(retries + 1, \"次尝试，等待\").concat(retryDelay, \"ms后重试...\"));\n                    await new Promise({\n                        \"useApiOperation.useCallback[execute]\": (resolve)=>setTimeout(resolve, retryDelay)\n                    }[\"useApiOperation.useCallback[execute]\"]);\n                }\n            }\n            // 所有重试都失败了\n            const errorMessage = lastError instanceof Error ? lastError.message : '操作失败';\n            const errorState = {\n                message: errorMessage,\n                timestamp: Date.now(),\n                ...lastError instanceof _lib_api__WEBPACK_IMPORTED_MODULE_1__.ApiError ? {\n                    code: lastError.code,\n                    status: lastError.status,\n                    details: lastError.details\n                } : {}\n            };\n            setError(errorState);\n            setLoading(false);\n            if (onError) {\n                onError(lastError);\n            }\n            throw lastError;\n        }\n    }[\"useApiOperation.useCallback[execute]\"], [\n        cancel\n    ]);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useApiOperation.useCallback[reset]\": ()=>{\n            cancel();\n            setLoading(false);\n            setError(null);\n            setData(null);\n        }\n    }[\"useApiOperation.useCallback[reset]\"], [\n        cancel\n    ]);\n    // 清理函数\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiOperation.useEffect\": ()=>{\n            return ({\n                \"useApiOperation.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"useApiOperation.useEffect\"];\n        }\n    }[\"useApiOperation.useEffect\"], []);\n    return {\n        loading,\n        error,\n        data,\n        execute,\n        cancel,\n        reset\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useApiState.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   expiryApi: () => (/* binding */ expiryApi),\n/* harmony export */   foodItemApi: () => (/* binding */ foodItemApi),\n/* harmony export */   inventoryApi: () => (/* binding */ inventoryApi),\n/* harmony export */   loadingManager: () => (/* binding */ loadingManager),\n/* harmony export */   recipeApi: () => (/* binding */ recipeApi),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/../node_modules/next/dist/build/polyfills/process.js\");\n// API服务层 - 处理所有后端API调用\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n// 全局加载状态管理\nclass LoadingManager {\n    setLoading(key, loading) {\n        if (loading) {\n            this.loadingStates.set(key, true);\n        } else {\n            this.loadingStates.delete(key);\n        }\n        this.notifyListeners();\n    }\n    isLoading(key) {\n        if (key) {\n            return this.loadingStates.has(key);\n        }\n        return this.loadingStates.size > 0;\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(new Map(this.loadingStates)));\n    }\n    constructor(){\n        this.loadingStates = new Map();\n        this.listeners = new Set();\n    }\n}\nconst loadingManager = new LoadingManager();\n// 错误类型定义\nclass ApiError extends Error {\n    constructor(message, status, code, details){\n        super(message), this.status = status, this.code = code, this.details = details;\n        this.name = 'ApiError';\n    }\n}\n// 网络错误检测\nfunction isNetworkError(error) {\n    return error instanceof TypeError && (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('connection'));\n}\n// 重试延迟计算（指数退避）\nfunction calculateRetryDelay(attempt) {\n    let baseDelay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1000;\n    return Math.min(baseDelay * Math.pow(2, attempt), 10000);\n}\n// 超时处理\nfunction withTimeout(promise, timeout) {\n    return Promise.race([\n        promise,\n        new Promise((_, reject)=>setTimeout(()=>reject(new Error('Request timeout')), timeout))\n    ]);\n}\n// 简单的内存缓存实现\nclass ApiCache {\n    get(key) {\n        const item = this.cache.get(key);\n        if (!item) return null;\n        return item.data;\n    }\n    set(key, data, ttl) {\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now() + ttl\n        });\n        // 设置过期清理\n        setTimeout(()=>{\n            this.cache.delete(key);\n        }, ttl);\n    }\n    has(key) {\n        const item = this.cache.get(key);\n        if (!item) return false;\n        // 检查是否过期\n        if (Date.now() > item.timestamp) {\n            this.cache.delete(key);\n            return false;\n        }\n        return true;\n    }\n    clear() {\n        this.cache.clear();\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\nconst apiCache = new ApiCache();\n// 请求拦截器\nconst requestInterceptors = [\n    // 添加时间戳防止缓存（仅对GET请求）\n    (config)=>{\n        if (config.method === 'GET' || !config.method) {\n            const url = new URL(config.url || '');\n            url.searchParams.append('_t', Date.now().toString());\n            config.url = url.toString();\n        }\n        return config;\n    }\n];\n// 响应拦截器\nconst responseInterceptors = [\n    // 示例：记录响应时间\n    async (response)=>{\n        console.debug(\"API响应: \".concat(response.url, \" - 状态: \").concat(response.status));\n        return response;\n    }\n];\n// 错误拦截器\nconst errorInterceptors = [\n    // 统一错误日志\n    async (error)=>{\n        console.error('API错误:', error);\n        return Promise.reject(error);\n    }\n];\n// 增强的API请求函数，支持拦截器、缓存、重试、超时和错误处理\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { timeout = 30000, retries = 3, retryDelay = 1000, skipAuth = false, cacheTime = 0, ...fetchOptions } = options;\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const requestKey = \"\".concat(fetchOptions.method || 'GET', \"_\").concat(endpoint);\n    const cacheKey = \"\".concat(requestKey, \"_\").concat(JSON.stringify(fetchOptions.body || ''));\n    // 检查缓存（仅对GET请求）\n    if (cacheTime > 0 && (fetchOptions.method === 'GET' || !fetchOptions.method)) {\n        if (apiCache.has(cacheKey)) {\n            return apiCache.get(cacheKey);\n        }\n    }\n    // 设置加载状态\n    loadingManager.setLoading(requestKey, true);\n    const defaultHeaders = {\n        'Content-Type': 'application/json'\n    };\n    // 添加认证token（如果存在且未跳过认证）\n    if (!skipAuth && \"object\" !== 'undefined') {\n        const token = localStorage.getItem('authToken');\n        if (token) {\n            defaultHeaders.Authorization = \"Bearer \".concat(token);\n        }\n    }\n    let config = {\n        ...fetchOptions,\n        headers: {\n            ...defaultHeaders,\n            ...fetchOptions.headers\n        }\n    };\n    // 应用请求拦截器\n    let finalUrl = url;\n    for (const interceptor of requestInterceptors){\n        const configWithUrl = interceptor({\n            ...config,\n            url: finalUrl\n        });\n        finalUrl = configWithUrl.url || finalUrl;\n        config = {\n            ...configWithUrl\n        };\n        // Remove the url property from config since it's not part of RequestInit\n        delete config.url;\n    }\n    let lastError = null;\n    // 重试逻辑\n    for(let attempt = 0; attempt <= retries; attempt++){\n        try {\n            // 执行请求（带超时）\n            let response = await withTimeout(fetch(finalUrl, config), timeout);\n            // 应用响应拦截器\n            for (const interceptor of responseInterceptors){\n                response = await interceptor(response);\n            }\n            // 尝试解析响应\n            let data;\n            const contentType = response.headers.get('content-type');\n            if (contentType && contentType.includes('application/json')) {\n                data = await response.json();\n            } else {\n                // 非JSON响应，尝试获取文本\n                const text = await response.text();\n                data = {\n                    message: text\n                };\n            }\n            // 清除加载状态\n            loadingManager.setLoading(requestKey, false);\n            // 检查HTTP状态\n            if (!response.ok) {\n                const errorMessage = data.error || data.message || \"HTTP \".concat(response.status);\n                // 特殊处理认证错误\n                if (response.status === 401) {\n                    // Token过期或无效，清除本地认证信息\n                    if (true) {\n                        localStorage.removeItem('authToken');\n                        localStorage.removeItem('user');\n                    }\n                    throw new ApiError('认证已过期，请重新登录', response.status, 'AUTH_EXPIRED');\n                }\n                // 特殊处理权限错误\n                if (response.status === 403) {\n                    throw new ApiError('权限不足', response.status, 'FORBIDDEN');\n                }\n                // 特殊处理服务器错误（可重试）\n                if (response.status >= 500 && attempt < retries) {\n                    throw new ApiError(errorMessage, response.status, 'SERVER_ERROR');\n                }\n                // 客户端错误（不重试）\n                if (response.status >= 400 && response.status < 500) {\n                    const errorResponse = {\n                        success: false,\n                        error: errorMessage,\n                        details: data\n                    };\n                    // 应用错误拦截器\n                    for (const interceptor of errorInterceptors){\n                        try {\n                            await interceptor(new ApiError(errorMessage, response.status));\n                        } catch (e) {\n                            console.error('错误拦截器执行失败:', e);\n                        }\n                    }\n                    return errorResponse;\n                }\n                throw new ApiError(errorMessage, response.status);\n            }\n            // 成功响应\n            const successResponse = data.success !== undefined ? data : {\n                success: true,\n                data\n            };\n            // 缓存响应（仅对GET请求）\n            if (cacheTime > 0 && (fetchOptions.method === 'GET' || !fetchOptions.method)) {\n                apiCache.set(cacheKey, successResponse, cacheTime);\n            }\n            return successResponse;\n        } catch (error) {\n            lastError = error instanceof Error ? error : new Error('Unknown error');\n            // 记录错误\n            console.error(\"API请求失败 [\".concat(endpoint, \"] (尝试 \").concat(attempt + 1, \"/\").concat(retries + 1, \"):\"), lastError);\n            // 应用错误拦截器\n            for (const interceptor of errorInterceptors){\n                try {\n                    await interceptor(lastError);\n                } catch (e) {\n                    console.error('错误拦截器执行失败:', e);\n                }\n            }\n            // 判断是否应该重试\n            const shouldRetry = attempt < retries && (isNetworkError(lastError) || lastError.message.includes('timeout') || lastError instanceof ApiError && lastError.status && lastError.status >= 500);\n            if (!shouldRetry) {\n                break;\n            }\n            // 等待后重试\n            if (attempt < retries) {\n                const delay = calculateRetryDelay(attempt, retryDelay);\n                console.log(\"等待 \".concat(delay, \"ms 后重试...\"));\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n            }\n        }\n    }\n    // 清除加载状态\n    loadingManager.setLoading(requestKey, false);\n    // 所有重试都失败了\n    const finalError = lastError || new Error('请求失败');\n    return {\n        success: false,\n        error: finalError instanceof ApiError ? finalError.message : '网络请求失败',\n        details: finalError instanceof ApiError ? finalError.details : undefined\n    };\n}\n// 库存管理API\nconst inventoryApi = {\n    // 获取库存列表\n    async getInventory (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.includeExpired) searchParams.append('includeExpired', params.includeExpired.toString());\n        if (params === null || params === void 0 ? void 0 : params.orderBy) searchParams.append('orderBy', params.orderBy);\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        const queryString = searchParams.toString();\n        return apiRequest(\"/inventory\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 获取库存统计\n    async getStats (familyId) {\n        const params = familyId ? \"?familyId=\".concat(familyId) : '';\n        return apiRequest(\"/inventory/stats\".concat(params));\n    },\n    // 添加库存项目\n    async addItem (item) {\n        return apiRequest('/inventory', {\n            method: 'POST',\n            body: JSON.stringify(item)\n        });\n    },\n    // 批量添加库存项目\n    async addBatch (items, familyId) {\n        return apiRequest('/inventory/batch', {\n            method: 'POST',\n            body: JSON.stringify({\n                items,\n                family_id: familyId\n            })\n        });\n    },\n    // 更新库存项目\n    async updateItem (id, updates) {\n        return apiRequest(\"/inventory/\".concat(id), {\n            method: 'PUT',\n            body: JSON.stringify(updates)\n        });\n    },\n    // 消费库存项目\n    async consumeItem (id, quantity) {\n        return apiRequest(\"/inventory/\".concat(id, \"/consume\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                quantity\n            })\n        });\n    },\n    // 删除库存项目\n    async deleteItem (id) {\n        return apiRequest(\"/inventory/\".concat(id), {\n            method: 'DELETE'\n        });\n    },\n    // 搜索库存\n    async searchInventory (keyword, params) {\n        const searchParams = new URLSearchParams({\n            q: keyword\n        });\n        if (params === null || params === void 0 ? void 0 : params.status) searchParams.append('status', params.status);\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        return apiRequest(\"/inventory/search?\".concat(searchParams.toString()));\n    },\n    // 获取即将过期的食材\n    async getExpiringItems () {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 7, familyId = arguments.length > 1 ? arguments[1] : void 0;\n        const params = new URLSearchParams({\n            days: days.toString()\n        });\n        if (familyId) params.append('familyId', familyId);\n        return apiRequest(\"/inventory/expiring?\".concat(params.toString()));\n    },\n    // 获取已过期的食材\n    async getExpiredItems (familyId) {\n        let autoUpdate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        const params = new URLSearchParams({\n            autoUpdate: autoUpdate.toString()\n        });\n        if (familyId) params.append('familyId', familyId);\n        return apiRequest(\"/inventory/expired?\".concat(params.toString()));\n    }\n};\n// 过期提醒API\nconst expiryApi = {\n    // 获取过期统计信息\n    async getStats (familyId) {\n        const params = familyId ? \"?familyId=\".concat(familyId) : '';\n        return apiRequest(\"/expiry/stats\".concat(params));\n    },\n    // 检查过期提醒\n    async checkReminders (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if (params === null || params === void 0 ? void 0 : params.expiringSoonDays) searchParams.append('expiringSoonDays', params.expiringSoonDays.toString());\n        if (params === null || params === void 0 ? void 0 : params.expiringTodayDays) searchParams.append('expiringTodayDays', params.expiringTodayDays.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/expiry/check\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 发送过期提醒\n    async sendReminders (params) {\n        return apiRequest('/expiry/send-reminders', {\n            method: 'POST',\n            body: JSON.stringify(params || {})\n        });\n    },\n    // 更新过期状态\n    async updateExpiredStatus (familyId) {\n        return apiRequest('/expiry/update-status', {\n            method: 'POST',\n            body: JSON.stringify({\n                familyId\n            })\n        });\n    },\n    // 获取即将过期的食材详情\n    async getExpiringItems (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.days) searchParams.append('days', params.days.toString());\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if ((params === null || params === void 0 ? void 0 : params.includeToday) !== undefined) searchParams.append('includeToday', params.includeToday.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/expiry/expiring-items\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 获取已过期的食材详情\n    async getExpiredItems (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if ((params === null || params === void 0 ? void 0 : params.autoUpdate) !== undefined) searchParams.append('autoUpdate', params.autoUpdate.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/expiry/expired-items\".concat(queryString ? \"?\".concat(queryString) : ''));\n    }\n};\n// 食材基础信息API\nconst foodItemApi = {\n    // 搜索食材\n    async search (keyword, params) {\n        // 这个API需要在后端实现\n        const searchParams = new URLSearchParams({\n            q: keyword\n        });\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        return apiRequest(\"/food-items/search?\".concat(searchParams.toString()));\n    },\n    // 获取分类列表\n    async getCategories () {\n        return apiRequest('/food-items/categories');\n    }\n};\n// 菜谱推荐API\nconst recipeApi = {\n    // 获取所有菜谱\n    async getAllRecipes (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.difficulty) searchParams.append('difficulty', params.difficulty);\n        if (params === null || params === void 0 ? void 0 : params.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        if (params === null || params === void 0 ? void 0 : params.tags) searchParams.append('tags', params.tags.join(','));\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 获取菜谱详情\n    async getRecipeById (id) {\n        return apiRequest(\"/recipes/\".concat(id));\n    },\n    // 搜索菜谱\n    async searchRecipes (keyword, params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.difficulty) searchParams.append('difficulty', params.difficulty);\n        if (params === null || params === void 0 ? void 0 : params.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        if (params === null || params === void 0 ? void 0 : params.tags) searchParams.append('tags', params.tags.join(','));\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/search/\".concat(encodeURIComponent(keyword)).concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 获取热门菜谱\n    async getPopularRecipes (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.difficulty) searchParams.append('difficulty', params.difficulty);\n        if (params === null || params === void 0 ? void 0 : params.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/popular\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 获取用户收藏的菜谱\n    async getFavoriteRecipes (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.offset) searchParams.append('offset', params.offset.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/favorites\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 切换菜谱收藏状态\n    async toggleFavorite (recipeId) {\n        return apiRequest(\"/recipes/\".concat(recipeId, \"/favorite\"), {\n            method: 'POST'\n        });\n    },\n    // 根据现有食材推荐菜谱\n    async getRecipesByIngredients (ingredients, params) {\n        return apiRequest('/recipes/recommend/by-ingredients', {\n            method: 'POST',\n            body: JSON.stringify({\n                ingredients,\n                minMatchRatio: (params === null || params === void 0 ? void 0 : params.minMatchRatio) || 0.5,\n                limit: (params === null || params === void 0 ? void 0 : params.limit) || 20,\n                familyId: params === null || params === void 0 ? void 0 : params.familyId\n            })\n        });\n    },\n    // 获取临期食材优先菜谱\n    async getRecipesForExpiringIngredients (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if (params === null || params === void 0 ? void 0 : params.daysAhead) searchParams.append('daysAhead', params.daysAhead.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/recommend/expiring\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 智能菜谱推荐\n    async getSmartRecommendations (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.includeExpiring) !== undefined) searchParams.append('includeExpiring', params.includeExpiring.toString());\n        if (params === null || params === void 0 ? void 0 : params.preferredDifficulty) searchParams.append('preferredDifficulty', params.preferredDifficulty);\n        if (params === null || params === void 0 ? void 0 : params.maxCookingTime) searchParams.append('maxCookingTime', params.maxCookingTime.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/recommend/smart\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 个性化菜谱推荐\n    async getPersonalizedRecommendations (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.includeNewRecipes) !== undefined) searchParams.append('includeNewRecipes', params.includeNewRecipes.toString());\n        if (params === null || params === void 0 ? void 0 : params.diversityFactor) searchParams.append('diversityFactor', params.diversityFactor.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/recommend/personalized\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 混合推荐算法\n    async getHybridRecommendations (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.familyId) searchParams.append('familyId', params.familyId);\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.contentWeight) searchParams.append('contentWeight', params.contentWeight.toString());\n        if (params === null || params === void 0 ? void 0 : params.collaborativeWeight) searchParams.append('collaborativeWeight', params.collaborativeWeight.toString());\n        if (params === null || params === void 0 ? void 0 : params.inventoryWeight) searchParams.append('inventoryWeight', params.inventoryWeight.toString());\n        if (params === null || params === void 0 ? void 0 : params.popularityWeight) searchParams.append('popularityWeight', params.popularityWeight.toString());\n        const queryString = searchParams.toString();\n        return apiRequest(\"/recipes/recommend/hybrid\".concat(queryString ? \"?\".concat(queryString) : ''));\n    },\n    // 获取菜谱分类和标签\n    async getCategories () {\n        return apiRequest('/recipes/categories');\n    },\n    // 初始化菜谱数据\n    async initializeRecipeData () {\n        return apiRequest('/recipes/initialize', {\n            method: 'POST'\n        });\n    }\n};\n// ========== 用户认证API ==========\nconst authApi = {\n    // 用户注册\n    async register (param) {\n        let { username, email, password } = param;\n        return apiRequest('/auth/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                username,\n                email,\n                password\n            })\n        });\n    },\n    // 用户登录\n    async login (param) {\n        let { email, password } = param;\n        return apiRequest('/auth/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n    },\n    // 获取当前用户信息\n    async getMe () {\n        return apiRequest('/auth/me', {\n            method: 'GET'\n        });\n    },\n    // 刷新token\n    async refresh (token) {\n        return apiRequest('/auth/refresh', {\n            method: 'POST',\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n    }\n};\n// 工具函数\nconst utils = {\n    // 计算剩余天数\n    calculateDaysLeft (expiryDate) {\n        const today = new Date();\n        const expiry = new Date(expiryDate);\n        const diffTime = expiry.getTime() - today.getTime();\n        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    },\n    // 格式化日期\n    formatDate (dateString) {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('zh-CN');\n    },\n    // 获取过期状态\n    getExpiryStatus (daysLeft) {\n        if (daysLeft < 0) return {\n            color: 'text-red-600',\n            bg: 'bg-red-50',\n            text: '已过期'\n        };\n        if (daysLeft <= 3) return {\n            color: 'text-orange-600',\n            bg: 'bg-orange-50',\n            text: \"\".concat(daysLeft, \"天后过期\")\n        };\n        if (daysLeft <= 7) return {\n            color: 'text-yellow-600',\n            bg: 'bg-yellow-50',\n            text: \"\".concat(daysLeft, \"天后过期\")\n        };\n        return {\n            color: 'text-green-600',\n            bg: 'bg-green-50',\n            text: \"\".concat(daysLeft, \"天后过期\")\n        };\n    },\n    // 转换后端数据格式到前端格式\n    transformInventoryItem (item) {\n        return {\n            id: item.id,\n            name: item.food_name || item.name,\n            category: item.category,\n            quantity: item.quantity,\n            unit: item.unit,\n            purchaseDate: item.added_date.split('T')[0],\n            expiryDate: item.expiry_date.split('T')[0],\n            daysLeft: this.calculateDaysLeft(item.expiry_date),\n            location: item.location,\n            notes: item.notes,\n            status: item.status\n        };\n    },\n    // 转换前端数据格式到后端格式\n    transformToBackendFormat (item) {\n        return {\n            name: item.name,\n            category: item.category,\n            quantity: item.quantity,\n            unit: item.unit,\n            expiry_date: new Date(item.expiryDate).toISOString(),\n            location: item.location,\n            notes: item.notes\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    inventoryApi,\n    expiryApi,\n    foodItemApi,\n    recipeApi,\n    authApi,\n    utils\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjames%5C%5CDesktop%5C%5Cbingxiang%5C%5Creaddy%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);