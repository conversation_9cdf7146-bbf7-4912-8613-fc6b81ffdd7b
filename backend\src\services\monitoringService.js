const fs = require('fs');
const path = require('path');
const { DatabaseWrapper } = require('../database/connection');
const loggingService = require('./loggingService');

class MonitoringService {
  constructor() {
    this.healthChecks = new Map();
    this.alerts = [];
    this.thresholds = {
      memoryUsage: 80, // 内存使用率阈值 (%)
      errorRate: 5,    // 错误率阈值 (%)
      responseTime: 2000, // 响应时间阈值 (ms)
      diskSpace: 90,   // 磁盘使用率阈值 (%)
      cpuUsage: 80     // CPU使用率阈值 (%)
    };
    this.startTime = new Date();
    this.lastHealthCheck = null;
  }

  // 注册健康检查
  registerHealthCheck(name, checkFunction) {
    this.healthChecks.set(name, checkFunction);
  }

  // 执行所有健康检查
  async performHealthChecks() {
    const results = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {},
      system: await this.getSystemMetrics(),
      uptime: this.getUptime()
    };

    // 执行注册的健康检查
    for (const [name, checkFn] of this.healthChecks) {
      try {
        const startTime = Date.now();
        const result = await checkFn();
        const duration = Date.now() - startTime;
        
        results.checks[name] = {
          status: result.status || 'healthy',
          message: result.message || 'OK',
          duration: `${duration}ms`,
          details: result.details || {}
        };

        if (result.status === 'unhealthy') {
          results.status = 'unhealthy';
        }
      } catch (error) {
        results.checks[name] = {
          status: 'unhealthy',
          message: error.message,
          error: true
        };
        results.status = 'unhealthy';
      }
    }

    this.lastHealthCheck = results;
    
    // 检查是否需要发送警报
    await this.checkAlerts(results);
    
    return results;
  }

  // 获取系统指标
  async getSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        usage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100) // %
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: Math.round(process.uptime()),
      pid: process.pid,
      version: process.version,
      platform: process.platform,
      arch: process.arch
    };
  }

  // 获取运行时间
  getUptime() {
    const uptime = process.uptime();
    const days = Math.floor(uptime / 86400);
    const hours = Math.floor((uptime % 86400) / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = Math.floor(uptime % 60);
    
    return {
      seconds: uptime,
      formatted: `${days}d ${hours}h ${minutes}m ${seconds}s`
    };
  }

  // 检查磁盘空间
  async checkDiskSpace() {
    try {
      const stats = fs.statSync(process.cwd());
      // 简化的磁盘空间检查，实际应用中可能需要更复杂的实现
      return {
        status: 'healthy',
        message: 'Disk space OK',
        details: {
          available: 'N/A (simplified check)',
          used: 'N/A (simplified check)'
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Cannot check disk space',
        details: { error: error.message }
      };
    }
  }

  // 检查数据库连接
  async checkDatabase() {
    try {
      const startTime = Date.now();
      
      // 执行简单查询测试连接
      await DatabaseWrapper.get('SELECT 1 as test');
      
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        message: 'Database connection OK',
        details: {
          responseTime: `${responseTime}ms`,
          type: 'SQLite'
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Database connection failed',
        details: { error: error.message }
      };
    }
  }

  // 检查AI服务状态
  async checkAIServices() {
    const results = {
      gemini: { 
        available: !!process.env.GEMINI_API_KEY,
        responseTime: null,
        lastError: null,
        status: 'unknown'
      },
      openai: { 
        available: !!process.env.OPENAI_API_KEY,
        responseTime: null,
        lastError: null,
        status: 'unknown'
      }
    };

    // 实际测试AI服务响应
    for (const [engine, config] of Object.entries(results)) {
      if (config.available) {
        try {
          const startTime = Date.now();
          await this.testAIEngine(engine);
          config.responseTime = Date.now() - startTime;
          config.status = 'healthy';
          config.lastChecked = new Date().toISOString();
        } catch (error) {
          config.status = 'unhealthy';
          config.lastError = error.message;
          config.lastChecked = new Date().toISOString();
          loggingService.logError('AI_SERVICE_CHECK', `${engine} 服务检查失败: ${error.message}`);
        }
      } else {
        config.status = 'disabled';
        config.lastError = 'API密钥未配置';
      }
    }

    const overallStatus = Object.values(results).some(s => s.status === 'healthy') ? 'healthy' : 'unhealthy';
  
    return {
      status: overallStatus,
      message: `AI服务状态检查完成`,
      details: results,
      timestamp: new Date().toISOString()
    };
  }

  // AI引擎测试方法
  async testAIEngine(engine) {
    const testPrompt = "测试连接";
    const timeout = 10000; // 10秒超时
  
    switch (engine) {
      case 'gemini':
        return await this.testGeminiService(testPrompt, timeout);
      case 'openai':
        return await this.testOpenAIService(testPrompt, timeout);
      default:
        throw new Error(`未知的AI引擎: ${engine}`);
    }
  }

  // Gemini服务测试
  async testGeminiService(prompt, timeout) {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
  
    return new Promise(async (resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Gemini服务响应超时'));
      }, timeout);

      try {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || 'gemini-1.5-flash' });
        
        const result = await model.generateContent(prompt);
        const response = await result.response;
        
        clearTimeout(timer);
        
        if (response && response.text) {
          resolve({ success: true, engine: 'gemini' });
        } else {
          reject(new Error('Gemini服务返回无效响应'));
        }
      } catch (error) {
        clearTimeout(timer);
        reject(new Error(`Gemini服务错误: ${error.message}`));
      }
    });
  }

  // OpenAI服务测试
  async testOpenAIService(prompt, timeout) {
    const OpenAI = require('openai');
  
    return new Promise(async (resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('OpenAI服务响应超时'));
      }, timeout);

      try {
        const openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });

        const completion = await openai.chat.completions.create({
          messages: [{ role: "user", content: prompt }],
          model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
          max_tokens: 10,
        });

        clearTimeout(timer);
        
        if (completion && completion.choices && completion.choices.length > 0) {
          resolve({ success: true, engine: 'openai' });
        } else {
          reject(new Error('OpenAI服务返回无效响应'));
        }
      } catch (error) {
        clearTimeout(timer);
        reject(new Error(`OpenAI服务错误: ${error.message}`));
      }
    });
  }

  // 检查警报条件
  async checkAlerts(healthData) {
    const alerts = [];
    
    // 检查内存使用率
    if (healthData.system.memory.usage > this.thresholds.memoryUsage) {
      alerts.push({
        type: 'memory',
        severity: 'warning',
        message: `High memory usage: ${healthData.system.memory.usage}%`,
        threshold: this.thresholds.memoryUsage,
        current: healthData.system.memory.usage
      });
    }

    // 检查错误率
    const stats = loggingService.getRequestStats();
    const errorRate = parseFloat(stats.errorRate);
    if (errorRate > this.thresholds.errorRate) {
      alerts.push({
        type: 'error_rate',
        severity: 'critical',
        message: `High error rate: ${stats.errorRate}`,
        threshold: this.thresholds.errorRate,
        current: errorRate
      });
    }

    // 检查数据库健康状态
    if (healthData.checks.database?.status === 'unhealthy') {
      alerts.push({
        type: 'database',
        severity: 'critical',
        message: 'Database connection failed',
        details: healthData.checks.database
      });
    }

    // 记录新警报
    for (const alert of alerts) {
      this.addAlert(alert);
    }

    return alerts;
  }

  // 添加警报
  addAlert(alert) {
    const alertWithTimestamp = {
      ...alert,
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      acknowledged: false
    };

    this.alerts.unshift(alertWithTimestamp);
    
    // 保持最近100个警报
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    // 记录警报到日志
    loggingService.logEvent('ALERT', alert.message, alert);

    return alertWithTimestamp;
  }

  // 确认警报
  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = new Date().toISOString();
      loggingService.logEvent('INFO', `Alert acknowledged: ${alert.message}`, { alertId });
      return true;
    }
    return false;
  }

  // 获取活跃警报
  getActiveAlerts() {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  // 获取所有警报
  getAllAlerts(limit = 50) {
    return this.alerts.slice(0, limit);
  }

  // 清理旧警报
  cleanupAlerts(daysToKeep = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const initialCount = this.alerts.length;
    this.alerts = this.alerts.filter(alert => 
      new Date(alert.timestamp) > cutoffDate
    );
    
    const removedCount = initialCount - this.alerts.length;
    if (removedCount > 0) {
      loggingService.logEvent('INFO', `Cleaned up ${removedCount} old alerts`);
    }
  }

  // 获取监控摘要
  getMonitoringSummary() {
    const stats = loggingService.getRequestStats();
    const activeAlerts = this.getActiveAlerts();
    
    return {
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      requests: {
        total: stats.total,
        errors: stats.errors,
        errorRate: stats.errorRate
      },
      alerts: {
        active: activeAlerts.length,
        total: this.alerts.length,
        recent: activeAlerts.slice(0, 5)
      },
      lastHealthCheck: this.lastHealthCheck?.timestamp || null,
      systemStatus: this.lastHealthCheck?.status || 'unknown'
    };
  }

  // 初始化默认健康检查
  initializeDefaultChecks() {
    this.registerHealthCheck('database', () => this.checkDatabase());
    this.registerHealthCheck('disk_space', () => this.checkDiskSpace());
    this.registerHealthCheck('ai_services', () => this.checkAIServices());
  }

  // 更新 getSystemHealth 方法，包含AI服务检查
  async getSystemHealth() {
    const checks = {
      database: await this.checkDatabase(),
      memory: this.checkMemory(),
      aiServices: await this.checkAIServices(), // 新增AI服务检查
      uptime: this.getUptime()
    };

    const overallStatus = Object.values(checks).every(check => 
      check.status === 'healthy'
    ) ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks,
      version: process.env.npm_package_version || '1.0.0'
    };
  }

  // 更新 getSystemHealth 方法，包含AI服务检查
  async getSystemHealth() {
    const checks = {
      database: await this.checkDatabase(),
      memory: this.checkMemory(),
      aiServices: await this.checkAIServices(), // 新增AI服务检查
      uptime: this.getUptime()
    };

    const overallStatus = Object.values(checks).every(check => 
      check.status === 'healthy'
    ) ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks,
      version: process.env.npm_package_version || '1.0.0'
    };
  }
}

// 创建单例实例
const monitoringService = new MonitoringService();
monitoringService.initializeDefaultChecks();

module.exports = monitoringService;
