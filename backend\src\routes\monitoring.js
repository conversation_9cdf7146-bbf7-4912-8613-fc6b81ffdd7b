const express = require('express');
const router = express.Router();
const monitoringService = require('../services/monitoringService');
const loggingService = require('../services/loggingService');
const { catchAsync } = require('../middleware/errorHandler');
const { authenticateToken } = require('../middleware/auth');

// 基础健康检查 - 公开接口
router.get('/health', catchAsync(async (req, res) => {
  const health = await monitoringService.performHealthChecks();
  
  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
}));

// 简单健康检查 - 用于负载均衡器
router.get('/ping', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 详细系统状态 - 需要认证
router.get('/status', authenticateToken, catchAsync(async (req, res) => {
  const [health, summary, stats] = await Promise.all([
    monitoringService.performHealthChecks(),
    monitoringService.getMonitoringSummary(),
    loggingService.getRequestStats()
  ]);

  res.json({
    health,
    summary,
    stats,
    timestamp: new Date().toISOString()
  });
}));

// 获取系统指标 - 需要认证
router.get('/metrics', authenticateToken, catchAsync(async (req, res) => {
  const metrics = await monitoringService.getSystemMetrics();
  const requestStats = loggingService.getRequestStats();
  
  res.json({
    system: metrics,
    requests: requestStats,
    uptime: monitoringService.getUptime(),
    timestamp: new Date().toISOString()
  });
}));

// 获取警报列表 - 需要认证
router.get('/alerts', authenticateToken, catchAsync(async (req, res) => {
  const { active, limit } = req.query;
  
  let alerts;
  if (active === 'true') {
    alerts = monitoringService.getActiveAlerts();
  } else {
    const alertLimit = parseInt(limit) || 50;
    alerts = monitoringService.getAllAlerts(alertLimit);
  }
  
  res.json({
    alerts,
    total: alerts.length,
    timestamp: new Date().toISOString()
  });
}));

// 确认警报 - 需要认证
router.post('/alerts/:alertId/acknowledge', authenticateToken, catchAsync(async (req, res) => {
  const { alertId } = req.params;
  const success = monitoringService.acknowledgeAlert(parseFloat(alertId));
  
  if (success) {
    res.json({ 
      message: 'Alert acknowledged successfully',
      alertId: parseFloat(alertId)
    });
  } else {
    res.status(404).json({ 
      error: 'Alert not found',
      alertId: parseFloat(alertId)
    });
  }
}));

// 获取日志文件 - 需要认证
router.get('/logs/:logType', authenticateToken, catchAsync(async (req, res) => {
  const { logType } = req.params;
  const { lines } = req.query;
  
  const validLogTypes = ['requests', 'errors', 'system', 'database', 'ai'];
  if (!validLogTypes.includes(logType)) {
    return res.status(400).json({ 
      error: 'Invalid log type',
      validTypes: validLogTypes
    });
  }
  
  const logLines = parseInt(lines) || 100;
  const logs = loggingService.getLogFile(`${logType}.log`, logLines);
  
  res.json({
    logType,
    lines: logs.length,
    logs,
    timestamp: new Date().toISOString()
  });
}));

// 清理旧日志和警报 - 需要认证
router.post('/cleanup', authenticateToken, catchAsync(async (req, res) => {
  const { days } = req.body;
  const daysToKeep = parseInt(days) || 7;
  
  // 清理日志
  loggingService.cleanupLogs(daysToKeep);
  
  // 清理警报
  monitoringService.cleanupAlerts(daysToKeep);
  
  res.json({
    message: 'Cleanup completed successfully',
    daysKept: daysToKeep,
    timestamp: new Date().toISOString()
  });
}));

// 重置统计信息 - 需要认证
router.post('/stats/reset', authenticateToken, catchAsync(async (req, res) => {
  loggingService.resetStats();
  
  res.json({
    message: 'Statistics reset successfully',
    timestamp: new Date().toISOString()
  });
}));

// 获取监控摘要 - 需要认证
router.get('/summary', authenticateToken, catchAsync(async (req, res) => {
  const summary = monitoringService.getMonitoringSummary();
  res.json(summary);
}));

// 添加AI服务专用监控端点
router.get('/ai-services', authenticateToken, async (req, res) => {
  try {
    const aiStatus = await monitoringService.checkAIServices();
    
    res.json({
      success: true,
      data: aiStatus
    });
  } catch (error) {
    console.error('AI服务检查失败:', error);
    res.status(500).json({
      success: false,
      error: 'AI服务检查失败',
      details: error.message
    });
  }
});

// AI服务性能测试端点
router.post('/ai-services/test', authenticateToken, async (req, res) => {
  try {
    const { engine, testImage } = req.body;
    
    if (!engine || !['gemini', 'openai', 'both'].includes(engine)) {
      return res.status(400).json({
        success: false,
        error: '请指定有效的AI引擎 (gemini, openai, both)'
      });
    }

    const results = {};
    const engines = engine === 'both' ? ['gemini', 'openai'] : [engine];
    
    for (const eng of engines) {
      const startTime = Date.now();
      try {
        await monitoringService.testAIEngine(eng);
        results[eng] = {
          status: 'success',
          responseTime: Date.now() - startTime,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        results[eng] = {
          status: 'failed',
          error: error.message,
          responseTime: Date.now() - startTime,
          timestamp: new Date().toISOString()
        };
      }
    }

    res.json({
      success: true,
      data: {
        testResults: results,
        summary: {
          totalTested: engines.length,
          successful: Object.values(results).filter(r => r.status === 'success').length,
          failed: Object.values(results).filter(r => r.status === 'failed').length
        }
      }
    });
  } catch (error) {
    console.error('AI服务测试失败:', error);
    res.status(500).json({
      success: false,
      error: 'AI服务测试失败',
      details: error.message
    });
  }
});

module.exports = router;
