# 设计文档

## 概述

AI智能冰箱助手采用分阶段实施策略，第一阶段专注于核心功能的Web网站实现，采用最轻量级的单体架构。后续阶段将逐步扩展到小程序和移动App，并升级为微服务架构。

### 实施阶段规划

**第一阶段（MVP）：** 
- 平台：Web网站
- 架构：单体应用 + SQLite
- 核心功能：拍照识别、基础库存管理、简单菜谱推荐
- AI引擎：Gemini Vision（主）+ GPT-4o-mini（备用）

**第二阶段（扩展）：**
- 平台：Web + 微信小程序 + 移动App
- 架构：微服务 + MySQL/PostgreSQL
- 完整功能：营养分析、多端同步、家庭共享、智能提醒
- AI引擎：多引擎支持（DeepSeek、豆包、Kimi等）

## 架构设计

### 第一阶段架构（MVP - Web网站）

```mermaid
graph TB
    subgraph "前端层"
        A[React Web应用]
    end
    
    subgraph "后端单体应用"
        B[Express.js服务器]
        C[用户认证模块]
        D[食材识别模块]
        E[库存管理模块]
        F[菜谱推荐模块]
    end
    
    subgraph "AI引擎层"
        G[Gemini Vision API]
        H[GPT-4o-mini API]
        I[引擎调度器]
    end
    
    subgraph "数据存储层"
        J[SQLite数据库]
        K[本地文件存储]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    
    D --> I
    I --> G
    I --> H
    
    C --> J
    D --> J
    E --> J
    F --> J
    
    D --> K
```

### 第二阶段架构（扩展版）

```mermaid
graph TB
    subgraph "多端客户端"
        A1[Web网站] 
        A2[微信小程序]
        A3[移动App]
    end
    
    subgraph "API网关层"
        B[API Gateway]
    end
    
    subgraph "微服务层"
        C[用户服务]
        D[识别服务]
        E[库存服务]
        F[营养服务]
        G[推荐服务]
        H[通知服务]
    end
    
    subgraph "AI引擎池"
        I[多引擎调度器]
        J[Gemini/GPT/DeepSeek/豆包/Kimi]
    end
    
    subgraph "数据存储"
        K[MySQL/PostgreSQL]
        L[Redis缓存]
        M[云存储OSS]
    end
    
    A1 --> B
    A2 --> B
    A3 --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    
    D --> I
    I --> J
    
    C --> K
    D --> K
    E --> K
    F --> K
    G --> K
    H --> K
    
    C --> L
    D --> L
    E --> L
    
    D --> M
```

### 第一阶段技术栈（MVP）

**后端技术栈：**
- **框架：** Node.js + Express.js
- **数据库：** SQLite（零配置，单文件）
- **文件存储：** 本地文件系统
- **身份认证：** JWT + bcrypt
- **图像处理：** Sharp（Node.js图像处理库）

**前端技术栈：**
- **框架：** React.js + TypeScript
- **UI组件库：** Ant Design
- **状态管理：** React Context + useReducer
- **HTTP客户端：** Axios
- **路由：** React Router

**AI服务：**
- **主识别引擎：** Google Gemini 2.5 Flash
- **备用引擎：** OpenAI GPT-4o-mini
- **简单调度：** 基础故障转移逻辑

### 第二阶段技术栈（扩展版）

**后端技术栈：**
- **框架：** Node.js + Express.js / Python + FastAPI
- **数据库：** MySQL/PostgreSQL + Redis
- **消息队列：** Redis Pub/Sub / RabbitMQ
- **文件存储：** 阿里云OSS / AWS S3
- **容器化：** Docker + Kubernetes

**前端技术栈：**
- **Web：** React.js + TypeScript + Ant Design
- **小程序：** 微信小程序原生开发
- **移动端：** React Native / Flutter

**AI服务：**
- **多引擎池：** Gemini、GPT、DeepSeek、豆包、Kimi、Claude
- **智能调度：** 负载均衡、成本优化、健康监控

## 第一阶段核心功能设计

### MVP功能范围

**包含功能：**
- 用户注册登录
- 拍照识别食材
- 基础库存管理（增删改查）
- 简单菜谱推荐
- 基础过期提醒

**暂不包含（第二阶段）：**
- 复杂的多引擎调度
- 营养分析
- 家庭共享
- 多端同步
- 智能通知系统

### 简化的AI引擎管理

第一阶段采用简单的双引擎故障转移机制：

```typescript
interface SimpleAIManager {
  // 主引擎识别
  recognizeWithGemini(image: Buffer): Promise<FoodItem>
  
  // 备用引擎识别
  recognizeWithGPT(image: Buffer): Promise<FoodItem>
  
  // 简单故障转移
  recognizeFood(image: Buffer): Promise<FoodItem>
}

// 简化的配置
interface AIConfig {
  gemini: {
    apiKey: string
    model: string
    enabled: boolean
  }
  openai: {
    apiKey: string
    model: string
    enabled: boolean
  }
  maxRetries: number
  timeout: number
}
```

## 第一阶段组件和接口设计

### 1. 识别服务 (Recognition Service) - MVP版本

**核心组件：**
- `ImageProcessor`: 图像预处理和优化
- `SimpleAIManager`: 简单的双引擎故障转移
- `ResultParser`: 识别结果解析和标准化

**主要接口：**

```typescript
interface RecognitionService {
  // 食材识别
  recognizeFood(image: File, options?: RecognitionOptions): Promise<FoodItem>
  
  // 批量识别
  batchRecognize(images: File[]): Promise<FoodItem[]>
  
  // 二维码识别
  extractQRCode(image: File): Promise<ProductInfo>
}

interface FoodItem {
  name: string
  category: string
  weight: number // 克
  freshness: number // 1-10
  storageMethod: string
  expiryDays: number
  nutrition: NutritionInfo
  confidence: number // 识别置信度
}
```

### 2. 库存服务 (Inventory Service) - MVP版本

**核心组件：**
- `InventoryManager`: 基础库存增删改查
- `ExpiryChecker`: 简单过期检查

**主要接口：**

```typescript
interface InventoryService {
  // 添加食材
  addItem(item: FoodItem, userId: string): Promise<InventoryItem>
  
  // 更新库存
  updateItem(itemId: string, updates: Partial<InventoryItem>): Promise<void>
  
  // 获取库存列表
  getInventory(userId: string, filters?: InventoryFilter): Promise<InventoryItem[]>
  
  // 删除/消费食材
  consumeItem(itemId: string, quantity: number): Promise<void>
  
  // 获取即将过期的食材
  getExpiringItems(userId: string, days: number): Promise<InventoryItem[]>
}

interface InventoryItem {
  id: string
  name: string
  category: string
  quantity: number
  unit: string
  addedDate: Date
  expiryDate: Date
  location: string // 冰箱位置
  imageUrl?: string
  userId: string
}
```

### 3. 菜谱推荐服务 (Recipe Service) - MVP版本

**核心组件：**
- `SimpleRecipeEngine`: 基础菜谱匹配
- `RecipeManager`: 菜谱数据管理

**主要接口：**

```typescript
interface RecipeService {
  // 根据现有食材推荐菜谱
  getRecipesByIngredients(ingredients: string[]): Promise<Recipe[]>
  
  // 获取所有菜谱
  getAllRecipes(): Promise<Recipe[]>
  
  // 根据ID获取菜谱详情
  getRecipeById(id: string): Promise<Recipe>
  
  // 搜索菜谱
  searchRecipes(query: string): Promise<Recipe[]>
}

interface Recipe {
  id: string
  name: string
  ingredients: string[]
  steps: string[]
  cookingTime: number // 分钟
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  imageUrl?: string
}
```

### 4. 用户服务 (User Service) - MVP版本

**核心组件：**
- `AuthManager`: 用户认证管理
- `UserManager`: 用户信息管理

**主要接口：**

```typescript
interface UserService {
  // 用户注册
  register(userData: RegisterData): Promise<User>
  
  // 用户登录
  login(email: string, password: string): Promise<LoginResult>
  
  // 获取用户信息
  getUserById(id: string): Promise<User>
  
  // 更新用户信息
  updateUser(id: string, updates: Partial<User>): Promise<void>
}

interface User {
  id: string
  username: string
  email: string
  avatarUrl?: string
  createdAt: Date
}

interface LoginResult {
  user: User
  token: string
}
```

## 数据模型设计

### 轻量级数据库方案 (SQLite)

适合小规模部署和快速原型开发，单文件数据库，零配置，易于备份和迁移。

```sql
-- 用户表 (SQLite版本)
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    avatar_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 健康档案表 (SQLite版本)
CREATE TABLE health_profiles (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    conditions TEXT, -- JSON格式: '["diabetes", "hypertension"]'
    allergies TEXT, -- JSON格式: '["nuts", "seafood"]'
    dietary_preferences TEXT, -- JSON格式: '["vegetarian", "low_sodium"]'
    target_calories INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 家庭组表 (SQLite版本)
CREATE TABLE families (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL,
    admin_id TEXT REFERENCES users(id),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 家庭成员表 (SQLite版本)
CREATE TABLE family_members (
    family_id TEXT REFERENCES families(id) ON DELETE CASCADE,
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member', -- 'admin', 'member'
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (family_id, user_id)
);
```

### 食材相关表 (SQLite版本)

```sql
-- 食材基础信息表 (SQLite版本)
CREATE TABLE food_items (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    default_expiry_days INTEGER,
    storage_method TEXT,
    nutrition_per_100g TEXT, -- JSON格式存储营养信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户库存表 (SQLite版本)
CREATE TABLE inventory (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    family_id TEXT REFERENCES families(id) ON DELETE CASCADE,
    food_item_id TEXT REFERENCES food_items(id),
    quantity REAL NOT NULL,
    unit TEXT DEFAULT 'g',
    added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    expiry_date DATETIME NOT NULL,
    location TEXT, -- '冷藏室', '冷冻室', '常温'
    image_url TEXT,
    notes TEXT,
    status TEXT DEFAULT 'active' -- 'active', 'consumed', 'expired'
);
```

### 菜谱相关表 (SQLite版本)

```sql
-- 菜谱表 (SQLite版本)
CREATE TABLE recipes (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL,
    description TEXT,
    cooking_time INTEGER, -- 分钟
    difficulty TEXT, -- 'easy', 'medium', 'hard'
    servings INTEGER DEFAULT 1,
    instructions TEXT, -- JSON格式: '["步骤1", "步骤2"]'
    tags TEXT, -- JSON格式: '["中式", "素食"]'
    nutrition_info TEXT, -- JSON格式存储营养信息
    rating REAL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 菜谱食材表 (SQLite版本)
CREATE TABLE recipe_ingredients (
    recipe_id TEXT REFERENCES recipes(id) ON DELETE CASCADE,
    food_item_id TEXT REFERENCES food_items(id),
    quantity REAL NOT NULL,
    unit TEXT,
    is_optional INTEGER DEFAULT 0, -- SQLite使用0/1代替布尔值
    PRIMARY KEY (recipe_id, food_item_id)
);

-- 用户收藏菜谱表 (SQLite版本)
CREATE TABLE user_favorite_recipes (
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
    recipe_id TEXT REFERENCES recipes(id) ON DELETE CASCADE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, recipe_id)
);
```

### 数据库方案对比

| 特性 | SQLite | MySQL | PostgreSQL |
|------|--------|-------|------------|
| **部署复杂度** | 零配置，单文件 | 中等，需要服务器 | 较高，需要配置 |
| **性能** | 适合小规模 | 中等规模 | 大规模高性能 |
| **并发支持** | 读多写少 | 良好 | 优秀 |
| **数据类型** | 基础类型 | 丰富类型 | 最丰富类型 |
| **扩展性** | 有限 | 良好 | 优秀 |
| **适用场景** | 原型开发、小应用 | 中小企业应用 | 企业级应用 |
| **维护成本** | 极低 | 中等 | 较高 |

### 轻量级部署建议

对于快速原型和小规模部署，推荐使用以下轻量级技术栈：

```yaml
# 轻量级部署配置
services:
  app:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data  # SQLite数据库文件
      - ./uploads:/app/uploads  # 本地文件存储
    environment:
      - NODE_ENV=production
      - DATABASE_URL=sqlite:./data/app.db
      - REDIS_URL=redis://redis:6379
  
  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

## 错误处理策略

### 1. AI识别错误处理

- **主引擎失败：** 自动切换到备用引擎
- **识别置信度低：** 提示用户手动确认
- **网络超时：** 重试机制（最多3次）
- **API限额：** 降级到本地OCR识别

### 2. 数据同步错误处理

- **网络断开：** 本地缓存，恢复后同步
- **数据冲突：** 时间戳优先 + 用户选择
- **同步失败：** 指数退避重试

### 3. 通知发送错误处理

- **推送失败：** 多渠道备用方案
- **模板错误：** 降级到默认模板
- **频率限制：** 智能合并相似通知

## 测试策略

### 1. 单元测试
- 各服务组件的核心逻辑测试
- AI识别结果解析测试
- 数据模型验证测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 第三方服务集成测试

### 3. 端到端测试
- 完整用户流程测试
- 多端同步测试
- 性能压力测试

### 4. AI模型测试
- 识别准确率测试
- 故障转移测试
- 响应时间测试

## 性能优化

### 1. 缓存策略
- **Redis缓存：** 用户会话、频繁查询数据
- **CDN缓存：** 静态资源、图片文件
- **应用缓存：** 营养数据库、菜谱数据

### 2. 数据库优化
- **索引优化：** 用户ID、食材类别、过期时间
- **分区表：** 按时间分区库存历史数据
- **读写分离：** 主从数据库架构

### 3. 图像处理优化
- **图片压缩：** 上传前自动压缩
- **异步处理：** 识别任务队列化
- **批量处理：** 多图片并行识别

## 安全考虑

### 1. 数据安全
- **加密存储：** 敏感数据AES加密
- **传输加密：** HTTPS/WSS协议
- **访问控制：** JWT token + 权限验证

### 2. API安全
- **频率限制：** 防止API滥用
- **参数验证：** 严格的输入验证
- **SQL注入防护：** 参数化查询

### 3. 隐私保护
- **数据脱敏：** 日志中隐藏敏感信息
- **权限隔离：** 家庭数据访问控制
- **数据删除：** 用户注销时完全清理