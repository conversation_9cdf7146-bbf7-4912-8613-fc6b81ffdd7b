# 实施计划

## 基于现有前端的后端开发策略

**前端状态：** ✅ 已完成高质量的Next.js前端界面，包含完整的食材管理、过期提醒、菜谱推荐功能

**开发重点：** 后端API开发、AI识别集成、用户认证系统

- [x] 1. 后端项目基础搭建





  - 创建Node.js + Express.js后端项目结构
  - 配置SQLite数据库连接和表结构
  - 设置CORS和中间件支持前端调用
  - 配置环境变量和基础错误处理
  - _需求: 需求6.4_

- [x] 2. 数据库设计与初始化







  - [x] 2.1 创建SQLite数据库表结构











    - 实现用户表、食材表、库存表、菜谱表的建表脚本
    - 编写数据库初始化和种子数据脚本
    - 创建数据库连接池和查询封装
    - _需求: 需求2.1, 需求4.3_

  - [x] 2.2 实现数据库操作层








    - 创建基础的CRUD操作封装
    - 实现数据库事务处理
    - 添加数据验证和约束检查
    - _需求: 需求2.1, 需求4.4_

- [x] 3. AI食材识别功能开发





  - [x] 3.1 集成AI识别引擎


    - 配置Google Gemini Vision API接入
    - 配置OpenAI GPT-4o-mini API作为备用
    - 实现双引擎故障转移逻辑
    - _需求: 需求1.1, 需求1.4_

  - [x] 3.2 实现图像处理API


    - 创建图像上传API接口（POST /api/recognition/upload）
    - 实现图像格式转换和压缩处理
    - 添加图像质量检查和优化
    - _需求: 需求1.1, 需求6.2_

  - [x] 3.3 实现识别结果处理


    - 创建AI识别API接口（POST /api/recognition/analyze）
    - 实现识别结果的标准化解析
    - 添加识别置信度评估和用户确认机制
    - _需求: 需求1.3, 需求1.5_

  - [x] 3.4 集成前端拍照识别功能


    - 在现有AddFoodModal中集成AI识别调用
    - 添加识别进度显示和结果展示
    - 实现识别结果的手动编辑和确认
    - _需求: 需求1.1, 需求1.5, 需求1.6, 需求6.2_

- [x] 4. AI识别功能本地测试版本部署






  - [x] 4.1 创建本地测试环境配置


    - 配置开发环境的启动脚本和环境变量
    - 实现前后端并发启动的开发服务器
    - 添加热重载和自动重启功能
    - _需求: 需求1.1, 需求6.4_

  - [x] 4.2 实现AI识别测试界面





    - 创建专门的AI识别测试页面
    - 添加批量测试和结果对比功能
    - 实现识别准确率统计和分析
    - _需求: 需求1.3, 需求1.5_

  - [x] 4.3 编写AI识别测试文档





    - 创建AI识别功能使用指南
    - 编写测试用例和预期结果说明
    - 添加常见问题和故障排除指南
    - _需求: 需求1.1, 需求6.5_

- [x] 5. 库存管理API开发







  - [x] 5.1 实现库存数据API





    - 创建库存增删改查API接口
    - 实现食材入库和出库逻辑
    - 添加库存数量和状态管理
    - _需求: 需求2.1_

  - [x] 5.2 实现过期提醒功能


    - 创建过期检查和提醒API接口
    - 实现临期食材筛选逻辑（7天、1天提醒）
    - 添加过期状态自动更新机制
    - _需求: 需求2.2, 需求2.3, 需求2.4_

  - [x] 5.3 集成前端库存管理


    - 替换前端Mock数据为真实API调用
    - 实现库存列表的实时更新
    - 添加过期提醒的后端数据支持
    - _需求: 需求2.1, 需求2.2, 需求6.3_

- [-] 6. 菜谱推荐API开发



  - [x] 6.1 创建菜谱数据管理


    - 设计并录入基础菜谱数据（至少50个菜谱）
    - 实现菜谱与食材的关联关系API
    - 创建菜谱分类和标签管理接口
    - _需求: 需求4.3_

  - [x] 6.2 实现菜谱推荐算法









    - 创建基于现有食材的菜谱匹配API
    - 实现临期食材优先推荐逻辑
    - 添加菜谱搜索和筛选接口
    - _需求: 需求4.1, 需求4.3_

  - [ ] 6.3 集成前端菜谱功能












    - 连接前端菜谱推荐组件到后端API
    - 实现菜谱收藏和用户偏好存储
    - 添加智能推荐算法的前端展示
    - _需求: 需求4.3, 需求4.4_

- [-] 7. 用户认证系统开发


  - [x] 7.1 实现用户认证API




    - 创建用户注册API接口（POST /api/auth/register）
    - 实现用户登录API接口（POST /api/auth/login）
    - 添加JWT token生成、验证和刷新机制
    - _需求: 需求6.1_


  - [x] 7.2 添加认证中间件 









    - 实现JWT token验证中间件
    - 创建用户权限检查逻辑
    - 添加密码加密和安全验证
    - _需求: 需求6.1_

  - [x] 7.3 创建前端认证页面




    - 实现用户登录页面组件
    - 实现用户注册页面组件
    - 集成前端路由保护和状态管理
    - _需求: 需求6.1, 需求6.4_

- [ ] 8. 系统集成与优化






  - [x] 8.1 完成前后端API集成





    - 替换所有前端Mock数据为真实API调用
    - 实现统一的错误处理和加载状态管理
    - 添加API请求拦截器和重试机制
    - _需求: 需求6.3, 需求6.4_

  - [ ] 8.2 添加系统监控和日志












    - 实现API请求日志记录
    - 添加错误监控和报警机制
    - 创建系统健康检查接口
    - _需求: 需求6.4_

  - [ ] 8.3 性能优化和测试
    - 优化AI识别和图片处理性能
    - 添加数据库查询优化和索引
    - 编写端到端测试用例
    - _需求: 需求6.2, 需求6.3_

- [ ] 9. 部署和发布准备
  - [ ] 9.1 创建生产环境配置
    - 配置生产环境的环境变量和安全设置
    - 实现数据库备份和恢复机制
    - 添加SSL证书和HTTPS配置
    - _需求: 需求6.4_

  - [ ] 9.2 编写部署文档
    - 创建项目安装和运行文档
    - 编写API接口文档
    - 添加用户使用指南和故障排除文档
    - _需求: 需求6.5_