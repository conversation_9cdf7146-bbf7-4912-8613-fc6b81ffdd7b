module.exports = {
  // 现有配置...
  
  aiServices: {
    healthCheck: {
      interval: 300000, // 5分钟检查一次
      timeout: 10000,   // 10秒超时
      retries: 2
    },
    thresholds: {
      responseTime: {
        warning: 5000,  // 5秒
        critical: 10000 // 10秒
      },
      errorRate: {
        warning: 10,    // 10%
        critical: 25    // 25%
      }
    },
    testPrompts: {
      gemini: "健康检查测试",
      openai: "健康检查测试"
    }
  }
};