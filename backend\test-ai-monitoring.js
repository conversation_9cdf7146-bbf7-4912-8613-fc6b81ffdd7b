const monitoringService = require('./src/services/monitoringService');

async function testAIMonitoring() {
  console.log('🔍 开始AI服务监控测试...\n');

  try {
    // 1. 测试AI服务健康检查
    console.log('1. 测试AI服务健康检查:');
    const healthCheck = await monitoringService.checkAIServices();
    console.log(JSON.stringify(healthCheck, null, 2));
    console.log('');

    // 2. 测试单个引擎
    console.log('2. 测试Gemini引擎:');
    try {
      const geminiResult = await monitoringService.testAIEngine('gemini');
      console.log('✅ Gemini测试成功:', geminiResult);
    } catch (error) {
      console.log('❌ Gemini测试失败:', error.message);
    }
    console.log('');

    console.log('3. 测试OpenAI引擎:');
    try {
      const openaiResult = await monitoringService.testAIEngine('openai');
      console.log('✅ OpenAI测试成功:', openaiResult);
    } catch (error) {
      console.log('❌ OpenAI测试失败:', error.message);
    }
    console.log('');

    // 3. 测试完整系统健康
    console.log('4. 测试完整系统健康(包含AI服务):');
    const systemHealth = await monitoringService.getSystemHealth();
    console.log(JSON.stringify(systemHealth, null, 2));

  } catch (error) {
    console.error('❌ AI监控测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testAIMonitoring();
}

module.exports = testAIMonitoring;